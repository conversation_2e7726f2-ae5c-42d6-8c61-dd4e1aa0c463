import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { Database } from '@spritely/supabase-types';
import { useEffect, useState } from 'react';

// Define the base activity type from Supabase
type ActivityLogBase = Database['public']['Tables']['activity_logs']['Row'];


// Extend the base activity type with additional fields
export interface Activity extends Omit<ActivityLogBase, 'details'> {
  // Override details to use a more specific type
  details: Record<string, unknown> | null;
  // Joined data
  user?: {
    id: string;
    email: string;
    user_metadata?: {
      first_name?: string;
      last_name?: string;
    };
  } | null;
  // Computed fields
  action?: string;
  time?: string;
  patient?: string;
  actor?: string;
}

export interface UseActivityOptions {
  limit?: number;
  resourceType?: string;
  userId?: string;
}

export function useActivity(options: UseActivityOptions = {}) {
  const { organization } = useAuth();
  const [activities, setActivities] = useState<Activity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    const fetchActivity = async () => {
      if (!organization) {
        setActivities([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Check if the activity_logs table exists
        const { error: tableCheckError } = await supabase
          .from('activity_logs')
          .select('id')
          .limit(1);

        // If the table doesn't exist or there's an error, use mock data for now
        if (tableCheckError) {
          console.warn('Activity logs table error:', tableCheckError.message);
          console.warn('Using mock data instead');

          // Generate mock data based on real data structure
          const orgId = organization.id === 'system-admin-no-org' ? 'mock-org-id' : organization.id;
          const mockActivities = generateMockActivities(orgId, options.limit || 10);
          setActivities(mockActivities);
          setTotalCount(mockActivities.length);
          setIsLoading(false);
          return;
        }

        // Start building the query
        let query = supabase
          .from('activity_logs')
          .select(`
            *
          `, { count: 'exact' });

        // Apply organization filter - skip for "All Organizations" view
        if (organization.id !== 'system-admin-no-org') {
          query = query.eq('organization_id', organization.id);
        }

        // Apply filters based on options
        if (options.resourceType) {
          query = query.eq('resource_type', options.resourceType);
        }

        if (options.userId) {
          query = query.eq('user_id', options.userId);
        }

        // Apply limit if specified
        if (options.limit) {
          query = query.limit(options.limit);
        }

        // Order by most recent first
        query = query.order('created_at', { ascending: false });

        const { data, error: fetchError, count } = await query;

        if (fetchError) throw new Error(fetchError.message);

        // Process the data to add computed fields
        const processedActivities = data.map((activity: Database['public']['Tables']['activity_logs']['Row']): Activity => {
          // Convert Json to Record<string, unknown> if it's an object
          const details = typeof activity.details === 'object' ?
            activity.details as Record<string, unknown> :
            activity.details ? JSON.parse(activity.details as string) as Record<string, unknown> :
            null;

          const createdAt = new Date(activity.created_at);
          const now = new Date();
          const diffMs = now.getTime() - createdAt.getTime();
          const diffMins = Math.floor(diffMs / 60000);
          const diffHours = Math.floor(diffMins / 60);
          const diffDays = Math.floor(diffHours / 24);

          let timeAgo;
          if (diffMins < 1) {
            timeAgo = 'Just now';
          } else if (diffMins < 60) {
            timeAgo = `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
          } else if (diffHours < 24) {
            timeAgo = `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
          } else {
            timeAgo = `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
          }

          // Format the action based on action_type and resource_type
          let action = activity.action_type || '';
          if (activity.action_type === 'create') {
            action = `Created ${activity.resource_type || 'item'}`;
          } else if (activity.action_type === 'update') {
            action = `Updated ${activity.resource_type || 'item'}`;
          } else if (activity.action_type === 'delete') {
            action = `Deleted ${activity.resource_type || 'item'}`;
          }

          // Get the actor name from user_id
          const actor = `User ${activity.user_id}`;

          // Get patient name if available in details
          let patient = '';
          if (details && typeof details === 'object') {
            patient = (details.patient_name as string) || '';
          }

          // Create a properly typed Activity object
          const typedActivity: Activity = {
            id: activity.id,
            organization_id: activity.organization_id,
            user_id: activity.user_id,
            action_type: activity.action_type,
            resource_type: activity.resource_type,
            resource_id: activity.resource_id,
            details: details, // Use the converted details
            created_at: activity.created_at,
            action,
            time: timeAgo,
            actor,
            patient
          };

          return typedActivity;
        });

        setActivities(processedActivities);
        if (count !== null) setTotalCount(count);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching activity logs:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch activity logs'));
        setIsLoading(false);
      }
    };

    fetchActivity();
  }, [organization, options.limit, options.resourceType, options.userId]);

  return { activities, isLoading, error, totalCount };
}

// Helper function to generate mock activity data if the table doesn't exist yet
function generateMockActivities(organizationId: string, count: number): Activity[] {
  const actionTypes = ['create', 'update', 'view'];
  const resourceTypes = ['patient', 'appointment', 'medical_record', 'prescription'];
  const userRoles = ['Dr. Martinez', 'Dr. Williams', 'Front Desk', 'Lab Tech', 'Nurse Johnson'];
  const patientNames = ['Sarah Johnson', 'Michael Chen', 'Emily Rodriguez', 'David Kim', 'Lisa Garcia'];

  return Array.from({ length: count }).map((_, index) => {
    const createdAt = new Date();
    createdAt.setMinutes(createdAt.getMinutes() - (index * 30 + Math.floor(Math.random() * 30)));

    const actionType = actionTypes[Math.floor(Math.random() * actionTypes.length)];
    const resourceType = resourceTypes[Math.floor(Math.random() * resourceTypes.length)];
    const userRole = userRoles[Math.floor(Math.random() * userRoles.length)];
    const patientName = patientNames[Math.floor(Math.random() * patientNames.length)];

    // Format the action based on action_type and resource_type
    let action = '';
    if (actionType === 'create') {
      action = `Added new ${resourceType}`;
    } else if (actionType === 'update') {
      action = `Updated ${resourceType}`;
    } else if (actionType === 'view') {
      action = `Viewed ${resourceType}`;
    }

    // Calculate time ago
    const now = new Date();
    const diffMs = now.getTime() - createdAt.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);

    let timeAgo;
    if (diffMins < 60) {
      timeAgo = `${diffMins} minutes ago`;
    } else {
      timeAgo = `${diffHours} hours ago`;
    }

    return {
      id: `mock-${index}`,
      organization_id: organizationId,
      user_id: `mock-user-${index}`,
      action_type: actionType,
      resource_type: resourceType,
      resource_id: `mock-resource-${index}`,
      details: { patient_name: patientName },
      created_at: createdAt.toISOString(),
      action,
      time: timeAgo,
      patient: patientName,
      actor: userRole
    };
  });
}
