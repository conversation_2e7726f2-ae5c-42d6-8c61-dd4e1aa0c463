import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { Database } from '@spritely/supabase-types';
import { useEffect, useState } from 'react';
import { applyOrganizationFilter, useOrganizationFilter } from './useOrganizationFilter';

// Base task type from Supabase
type TaskBase = Database['public']['Tables']['tasks']['Row'];

// Extend the base task type with additional fields
export interface Task extends Omit<TaskBase, 'priority' | 'metadata' | 'related_to'> {
  // Override priority to match the database schema
  priority: 'high' | 'medium' | 'low' | 'urgent';
  // Override metadata and related_to to use more specific types
  metadata: Record<string, unknown> | null;
  related_to: Record<string, unknown> | null;
  // Computed fields
  due?: string;
}

export interface UseTasksOptions {
  limit?: number;
  status?: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority?: 'high' | 'medium' | 'low';
  assignedToCurrentUser?: boolean;
  dueSoon?: boolean;
}

export function useTasks(options: UseTasksOptions = {}) {
  const { organization, user } = useAuth();
  const organizationFilter = useOrganizationFilter();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    const fetchTasks = async () => {
      if (!organization) {
        setTasks([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Start building the query
        let query = supabase
          .from('tasks')
          .select(`
            *
          `, { count: 'exact' });

        // Apply organization filter
        query = applyOrganizationFilter(query, organizationFilter);

        // Apply filters based on options
        if (options.status) {
          query = query.eq('status', options.status);
        } else {
          // Default to pending and in_progress tasks
          query = query.in('status', ['pending', 'in_progress']);
        }

        if (options.priority) {
          query = query.eq('priority', options.priority);
        }

        if (options.assignedToCurrentUser && user) {
          // First get the healthcare provider ID for the current user
          const { data: providerData } = await supabase
            .from('healthcare_providers')
            .select('id')
            .eq('user_id', user.id)
            .single();

          if (providerData?.id) {
            query = query.eq('assigned_to', providerData.id);
          }
        }

        if (options.dueSoon) {
          const today = new Date();
          const nextWeek = new Date(today);
          nextWeek.setDate(nextWeek.getDate() + 7);

          query = query
            .lte('due_date', nextWeek.toISOString())
            .gte('due_date', today.toISOString());
        }

        // Apply limit if specified
        if (options.limit) {
          query = query.limit(options.limit);
        }

        // Order by priority (high first) and then due date (soonest first)
        query = query
          .order('priority', { ascending: false })
          .order('due_date', { ascending: true, nullsFirst: false });

        const { data, error: fetchError, count } = await query;

        if (fetchError) throw new Error(fetchError.message);

        // Process the data to add computed fields
        const processedTasks = data.map((task: Database['public']['Tables']['tasks']['Row']) => {
          // Convert Json to Record<string, unknown> if it's an object
          const relatedTo = typeof task.related_to === 'object' ?
            task.related_to as Record<string, unknown> :
            task.related_to ? JSON.parse(task.related_to as string) as Record<string, unknown> :
            null;

          let dueText = 'No deadline';

          if (task.due_date) {
            const dueDate = new Date(task.due_date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            const isToday = dueDate >= today && dueDate < tomorrow;
            const isTomorrow = dueDate >= tomorrow && dueDate < new Date(tomorrow.getTime() + 86400000);

            if (isToday) {
              dueText = 'Today';
            } else if (isTomorrow) {
              dueText = 'Tomorrow';
            } else {
              dueText = dueDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            }
          }

          // Create a properly typed Task object
          const typedTask: Task = {
            id: task.id,
            organization_id: task.organization_id,
            department_id: task.department_id,
            title: task.title,
            description: task.description,
            priority: task.priority,
            status: task.status,
            assigned_to: task.assigned_to,
            assigned_by: task.assigned_by,
            due_date: task.due_date,
            completed_at: task.completed_at,
            related_to: relatedTo,
            metadata: task.metadata ? {} : null,
            created_at: task.created_at,
            updated_at: task.updated_at,
            // Add computed fields
            due: dueText
          };

          return typedTask;
        });

        setTasks(processedTasks);
        if (count !== null) setTotalCount(count);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching tasks:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch tasks'));
        setIsLoading(false);
      }
    };

    fetchTasks();
  }, [organization, organizationFilter, user, options.limit, options.status, options.priority, options.assignedToCurrentUser, options.dueSoon]);

  return { tasks, isLoading, error, totalCount };
}
