import { Organization } from '@/contexts/auth-context-types';
import { OrganizationItem, SearchOptions, SortOptions } from '../types/organization-selector-types';

/**
 * Organization utilities for filtering, sorting, and transforming data
 */

/**
 * Transform Organization to OrganizationItem with additional metadata
 */
export function transformToOrganizationItem(
  org: Organization,
  currentOrgId?: string,
  userPermissions?: {
    canEdit?: boolean;
    canManage?: boolean;
    isSystemAdmin?: boolean;
  }
): OrganizationItem {
  const isCurrent = currentOrgId === org.id;

  return {
    ...org,
    isCurrent,
    canEdit: userPermissions?.canEdit || userPermissions?.isSystemAdmin || false,
    canManage: userPermissions?.canManage || userPermissions?.isSystemAdmin || false,
    disabled: false, // Can be overridden based on business logic
    metadata: {
      lastSelected: isCurrent,
      userRole: 'member' // This would come from actual user role data
    }
  };
}

/**
 * Filter organizations based on search term
 */
export function filterOrganizations(
  organizations: OrganizationItem[],
  searchTerm: string,
  options: SearchOptions = {}
): OrganizationItem[] {
  if (!searchTerm.trim()) {
    return organizations;
  }

  const {
    searchFields = ['name'],
    caseSensitive = false,
    fuzzySearch = false,
    customSearch
  } = options;

  // Use custom search function if provided
  if (customSearch) {
    return customSearch(organizations, searchTerm);
  }

  const normalizedSearchTerm = caseSensitive ? searchTerm : searchTerm.toLowerCase();

  return organizations.filter(org => {
    return searchFields.some(field => {
      const fieldValue = org[field];
      if (typeof fieldValue !== 'string') return false;

      const normalizedFieldValue = caseSensitive ? fieldValue : fieldValue.toLowerCase();

      if (fuzzySearch) {
        return fuzzyMatch(normalizedFieldValue, normalizedSearchTerm);
      } else {
        return normalizedFieldValue.includes(normalizedSearchTerm);
      }
    });
  });
}

/**
 * Simple fuzzy matching algorithm
 */
function fuzzyMatch(text: string, pattern: string): boolean {
  let patternIndex = 0;
  let textIndex = 0;

  while (patternIndex < pattern.length && textIndex < text.length) {
    if (pattern[patternIndex] === text[textIndex]) {
      patternIndex++;
    }
    textIndex++;
  }

  return patternIndex === pattern.length;
}

/**
 * Sort organizations based on criteria
 */
export function sortOrganizations(
  organizations: OrganizationItem[],
  options: SortOptions = {}
): OrganizationItem[] {
  const {
    sortBy = 'name',
    sortDirection = 'asc',
    customSort
  } = options;

  // Use custom sort function if provided
  if (customSort) {
    return [...organizations].sort(customSort);
  }

  return [...organizations].sort((a, b) => {
    // Always put current organization first
    if (a.isCurrent && !b.isCurrent) return -1;
    if (!a.isCurrent && b.isCurrent) return 1;

    const aValue = a[sortBy];
    const bValue = b[sortBy];

    // Handle null/undefined values
    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return 1;
    if (bValue == null) return -1;

    // Compare values
    let comparison = 0;
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      comparison = aValue.localeCompare(bValue);
    } else if (typeof aValue === 'number' && typeof bValue === 'number') {
      comparison = aValue - bValue;
    } else {
      comparison = String(aValue).localeCompare(String(bValue));
    }

    return sortDirection === 'asc' ? comparison : -comparison;
  });
}

/**
 * Group organizations by criteria
 */
export function groupOrganizations(
  organizations: OrganizationItem[],
  groupBy: keyof OrganizationItem | ((org: OrganizationItem) => string)
): Record<string, OrganizationItem[]> {
  const groups: Record<string, OrganizationItem[]> = {};

  organizations.forEach(org => {
    const groupKey = typeof groupBy === 'function'
      ? groupBy(org)
      : String(org[groupBy] || 'Other');

    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(org);
  });

  return groups;
}

/**
 * Get organization statistics
 */
export function getOrganizationStats(organizations: OrganizationItem[]): {
  total: number;
  current: number;
  editable: number;
  manageable: number;
  disabled: number;
} {
  return organizations.reduce(
    (stats, org) => ({
      total: stats.total + 1,
      current: stats.current + (org.isCurrent ? 1 : 0),
      editable: stats.editable + (org.canEdit ? 1 : 0),
      manageable: stats.manageable + (org.canManage ? 1 : 0),
      disabled: stats.disabled + (org.disabled ? 1 : 0)
    }),
    { total: 0, current: 0, editable: 0, manageable: 0, disabled: 0 }
  );
}

/**
 * Validate organization data
 */
export function validateOrganization(org: Partial<Organization>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!org.name || org.name.trim().length === 0) {
    errors.push('Organization name is required');
  }

  if (org.name && org.name.length > 100) {
    errors.push('Organization name must be less than 100 characters');
  }

  if (!org.id) {
    errors.push('Organization ID is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Format organization display name
 */
export function formatOrganizationName(
  org: OrganizationItem,
  options: {
    maxLength?: number;
    showCurrent?: boolean;
    showRole?: boolean;
  } = {}
): string {
  const { maxLength = 50, showCurrent = true, showRole = false } = options;

  let name = org.name;

  // Truncate if necessary
  if (name.length > maxLength) {
    name = name.substring(0, maxLength - 3) + '...';
  }

  // Add current indicator
  if (showCurrent && org.isCurrent) {
    name += ' (Current)';
  }

  // Add role indicator
  if (showRole && org.metadata?.userRole) {
    name += ` - ${org.metadata.userRole}`;
  }

  return name;
}

/**
 * Get organization permissions for current user
 */
export function getOrganizationPermissions(
  org: OrganizationItem,
  userRoles: string[] = []
): {
  canView: boolean;
  canEdit: boolean;
  canManage: boolean;
  canDelete: boolean;
} {
  const isSystemAdmin = userRoles.includes('system_admin');
  const isOrgAdmin = userRoles.includes('org_admin');

  return {
    canView: true, // Assume all visible orgs can be viewed
    canEdit: org.canEdit || isSystemAdmin || isOrgAdmin,
    canManage: org.canManage || isSystemAdmin,
    canDelete: isSystemAdmin // Only system admins can delete orgs
  };
}

/**
 * Create a new organization item with defaults
 */
export function createOrganizationItem(
  partial: Partial<Organization>,
  defaults: Partial<OrganizationItem> = {}
): OrganizationItem {
  return {
    id: '',
    name: '',
    type: 'clinic',
    owner_id: '',
    settings: {},
    billing_info: {},
    subscription_tier: 'free',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    isCurrent: false,
    canEdit: false,
    canManage: false,
    disabled: false,
    metadata: {},
    ...defaults,
    ...partial
  };
}
