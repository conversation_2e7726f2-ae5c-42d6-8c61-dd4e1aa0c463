// Main component
export { HeaderOrganizationSelector as OrganizationSelector } from './OrganizationSelector';

// Sub-components
export { OrganizationActions } from './components/OrganizationActions';
export { OrganizationDropdown } from './components/OrganizationDropdown';
export { OrganizationItem as OrganizationItemComponent } from './components/OrganizationItem';
export { OrganizationList } from './components/OrganizationList';
export { OrganizationSearch } from './components/OrganizationSearch';

// Hooks
export { useKeyboardNavigation } from './hooks/useKeyboardNavigation';
export { useOrganizationSearch } from './hooks/useOrganizationSearch';
export { useOrganizationSelector } from './hooks/useOrganizationSelector';

// Types
export type {
    AccessibilityOptions,
    AnimationOptions, KeyboardNavigationState, OrganizationItem, OrganizationSelectorCallbacks, OrganizationSelectorConfig, OrganizationSelectorProps, OrganizationSelectorState, SearchOptions,
    SortOptions,
    VirtualizationOptions
} from './types/organization-selector-types';

// Utils
export {
    createOrganizationItem, filterOrganizations, formatOrganizationName,
    getOrganizationPermissions, getOrganizationStats, groupOrganizations, sortOrganizations, transformToOrganizationItem, validateOrganization
} from './utils/organization-utils';

export {
    debounce, getFirstNavigableIndex, getKeyboardShortcuts, getLastNavigableIndex, getNextNavigableIndex, handleKeyboardNavigation, isSearchKey, scrollIntoView
} from './utils/keyboard-utils';

