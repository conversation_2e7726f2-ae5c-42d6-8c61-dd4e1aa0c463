import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import React from 'react';
import { useKeyboardNavigation } from '../hooks/useKeyboardNavigation';
import { useOrganizationSearch } from '../hooks/useOrganizationSearch';
import { OrganizationItem as OrganizationItemType } from '../types/organization-selector-types';
import { OrganizationActions } from './OrganizationActions';
import { OrganizationList } from './OrganizationList';
import { OrganizationSearch } from './OrganizationSearch';

interface OrganizationDropdownProps {
  trigger: React.ReactNode;
  organizations: OrganizationItemType[];
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSelect: (org: OrganizationItemType) => void;
  onEdit?: (org: OrganizationItemType) => void;
  onManage?: (org: OrganizationItemType) => void;
  onCreate?: () => void;
  onManageAll?: () => void;
  onViewAll?: () => void;
  title?: string;
  searchPlaceholder?: string;
  showSearch?: boolean;
  searchThreshold?: number;
  showActions?: boolean;
  canCreate?: boolean;
  canManageAll?: boolean;
  maxHeight?: number;
  className?: string;
  disabled?: boolean;
}

export function OrganizationDropdown({
  trigger,
  organizations,
  isOpen,
  onOpenChange,
  onSelect,
  onEdit,
  onManage,
  onCreate,
  onManageAll,
  onViewAll,
  title = 'Switch Organization',
  searchPlaceholder = 'Search organizations...',
  showSearch = true,
  searchThreshold = 3,
  showActions = true,
  canCreate = false,
  canManageAll = false,
  maxHeight = 300,
  className,
  disabled = false
}: OrganizationDropdownProps) {

  // Search functionality
  const {
    searchTerm,
    filteredOrganizations,
    searchStats,
    handleSearchInput,
    handleSearchKeyDown,
    handleSearchClear,
    highlightMatch
  } = useOrganizationSearch(organizations);

  const handleOpenChange = (open: boolean) => {
    onOpenChange(open);
  };

  const handleSelect = (org: OrganizationItemType) => {
    onSelect(org);
    onOpenChange(false); // Close the dropdown after selection
  };

  // Keyboard navigation
  const {
    focusedIndex,
    getItemProps,
    getContainerProps
  } = useKeyboardNavigation(
    filteredOrganizations,
    isOpen,
    handleSelect,
    () => onOpenChange(false)
  );

  const shouldShowSearch = showSearch && organizations.length >= searchThreshold;

  const handleCreate = () => {
    onCreate?.();
    onOpenChange(false); // Close the dropdown after action
  };

  const handleManageAll = () => {
    onManageAll?.();
    onOpenChange(false); // Close the dropdown after action
  };

  const handleViewAll = () => {
    onViewAll?.();
    onOpenChange(false); // Close the dropdown after action
  };


  return (
    <DropdownMenu open={isOpen} onOpenChange={handleOpenChange}>
      <DropdownMenuTrigger asChild disabled={disabled}>
        {trigger}
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="center"
        className={cn('w-80 p-0', className)}
        onCloseAutoFocus={(e) => e.preventDefault()}
      >
        {/* Header */}
        <DropdownMenuLabel className="px-3 py-2 text-sm font-medium">
          {title}
        </DropdownMenuLabel>

        <DropdownMenuSeparator className="my-0" />

        {/* Search */}
        {shouldShowSearch && (
          <>
            <div className="px-3 py-2">
              <OrganizationSearch
                value={searchTerm}
                placeholder={searchPlaceholder}
                onChange={handleSearchInput}
                onKeyDown={handleSearchKeyDown}
                onClear={handleSearchClear}
                autoFocus={isOpen}
                searchStats={searchStats}
              />
            </div>
            <DropdownMenuSeparator className="my-0" />
          </>
        )}

        {/* Organization List */}
        <OrganizationList
          organizations={filteredOrganizations}
          focusedIndex={focusedIndex}
          maxHeight={maxHeight}
          onSelect={handleSelect}
          onEdit={onEdit}
          onManage={onManage}
          highlightText={(text) => highlightMatch(text, searchTerm)}
          getItemProps={getItemProps}
          getContainerProps={getContainerProps}
          emptyMessage={
            searchStats.hasSearch
              ? `No organizations found matching "${searchTerm}"`
              : 'No organizations available'
          }
        />

        {/* Actions */}
        {showActions && (
          <OrganizationActions
            canCreate={canCreate}
            canManageAll={canManageAll}
            onCreate={handleCreate}
            onManageAll={handleManageAll}
            onViewAll={handleViewAll}
          />
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
