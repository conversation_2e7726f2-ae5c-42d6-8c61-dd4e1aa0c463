import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { useEffect, useState } from 'react';

// Update the Appointment interface to match the expected format
interface Appointment {
  id: string;
  patient_id: string;
  provider_id: string;
  date: string;
  time: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_show' | 'checked_in' | 'in_progress';
  patient_first_name?: string;
  patient_last_name?: string;
  provider_first_name?: string;
  provider_last_name?: string;
  type?: string;
  duration?: number;
}

// Import the generated types from Supabase
import { Database } from '@spritely/supabase-types';

// Define the database record shape for type safety
type AppointmentRecord = Database['public']['Tables']['appointments']['Row'] & {
  patient?: { first_name?: string; last_name?: string } | null;
  provider?: { first_name?: string; last_name?: string } | null;
}

export default function Appointments() {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const { organization } = useAuth();

  useEffect(() => {
    async function fetchAppointments() {
      if (!organization) return;

      try {
        setIsLoading(true);
        // Using the correct table name from generated types
        let query = supabase
          .from('appointments')
          .select(`
            *,
            patient:patients(first_name, last_name),
            provider:healthcare_providers(first_name, last_name)
          `);

        // Apply organization filter - skip for "All Organizations" view
        if (organization.id !== 'system-admin-no-org') {
          query = query.eq('organization_id', organization.id);
        }

        const { data, error } = await query.order('appointment_date', { ascending: true });

        if (error) {
          throw error;
        }

        if (data) {
          // Transform the data to the expected format
          const formattedAppointments = data.map((item: AppointmentRecord) => {
            // Extract date and time from appointment_date
            // Format: "2023-01-15T14:30:00" -> { date: "2023-01-15", time: "14:30" }
            const [dateStr, timeStr] = (item.appointment_date || '').split('T');
            const formattedTime = timeStr ? timeStr.substring(0, 5) : '';

            return {
              id: item.id,
              patient_id: item.patient_id || '',
              provider_id: item.provider_id || '',
              date: dateStr || '',
              time: formattedTime,
              status: item.status,
              patient_first_name: item.patient?.first_name,
              patient_last_name: item.patient?.last_name,
              provider_first_name: item.provider?.first_name,
              provider_last_name: item.provider?.last_name,
              type: item.reason || 'Consultation',
              duration: item.duration_minutes || 30
            };
          });

          setAppointments(formattedAppointments);
        }
      } catch (error) {
        console.error('Error fetching appointments:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchAppointments();
  }, [organization]);

  // Filter appointments based on search query
  const filteredAppointments = appointments.filter((appointment: Appointment) => {
    const patientName = `${appointment.patient_first_name || ''} ${appointment.patient_last_name || ''}`.toLowerCase();
    const providerName = `${appointment.provider_first_name || ''} ${appointment.provider_last_name || ''}`.toLowerCase();
    return patientName.includes(searchQuery.toLowerCase()) ||
           providerName.includes(searchQuery.toLowerCase());
  });

  // Filter appointments by status for different tabs
  const upcomingAppointments = filteredAppointments.filter(
    (app: Appointment) => app.status === 'scheduled' || app.status === 'checked_in' || app.status === 'in_progress'
  );

  // Group appointments by date
  const groupAppointmentsByDate = (appointments: Appointment[]) => {
    const grouped: Record<string, Appointment[]> = {};

    appointments.forEach(appointment => {
      const date = appointment.date;
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(appointment);
    });

    return grouped;
  };

  const groupedUpcoming = groupAppointmentsByDate(upcomingAppointments);

  return (
    <div className="w-full">
      <div className="flex flex-col space-y-4">
        <h1 className="text-2xl font-bold">Appointments</h1>

        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <p>Loading appointments...</p>
          </div>
        ) : appointments.length === 0 ? (
          <div className="flex justify-center items-center h-40 border rounded-lg p-6 bg-muted/50">
            <p>No appointments found.</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="relative w-full max-w-sm">
                <input
                  type="text"
                  placeholder="Search appointments..."
                  className="w-full rounded-md border border-input px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  value={searchQuery}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="grid gap-4">
              {Object.entries(groupedUpcoming).map(([date, dateAppointments]) => (
                <div key={date} className="space-y-2">
                  <h3 className="font-medium">{date}</h3>
                  <div className="grid gap-2">
                    {dateAppointments.map((appointment) => (
                      <div
                        key={appointment.id}
                        className="flex justify-between items-center p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                      >
                        <div className="space-y-1">
                          <div className="font-medium">
                            {appointment.patient_first_name} {appointment.patient_last_name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {appointment.time}
                            {' with '}
                            {appointment.provider_first_name} {appointment.provider_last_name}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}