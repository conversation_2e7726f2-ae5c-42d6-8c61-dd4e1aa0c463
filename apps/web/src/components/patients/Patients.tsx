import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import {
    Calendar,
    FileText,
    Filter,
    Mail,
    Phone,
    Search,
    UserPlus,
    Users
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface Patient {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  date_of_birth?: string;
  last_visit?: string;
  gender?: string;
}

export default function Patients() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const { organization } = useAuth();

  useEffect(() => {
    async function fetchPatients() {
      if (!organization) return;

      try {
        setIsLoading(true);
        let query = supabase
          .from('patients')
          .select('*');

        // Apply organization filter - skip for "All Organizations" view
        if (organization.id !== 'system-admin-no-org') {
          query = query.eq('organization_id', organization.id);
        }

        const { data, error } = await query;

        if (error) {
          throw error;
        }

        if (data) {
          setPatients(data as Patient[]);
        }
      } catch (error) {
        console.error('Error fetching patients:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchPatients();
  }, [organization]);

  // Filter patients based on search query
  const filteredPatients = patients.filter(patient => {
    const fullName = `${patient.first_name} ${patient.last_name}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase()) ||
           (patient.email && patient.email.toLowerCase().includes(searchQuery.toLowerCase()));
  });

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string | undefined) => {
    if (!dateOfBirth) return 'N/A';

    const dob = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
      age--;
    }

    return age;
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Patients</h1>
          <p className="text-muted-foreground">
            Manage and view patient information
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button size="sm">
            <UserPlus className="mr-2 h-4 w-4" />
            <span>Add Patient</span>
          </Button>
        </div>
      </div>

      {/* Search and filter bar */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search patients by name or email..."
            className="w-full pl-8 focus-visible:ring-primary"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button variant="outline" size="icon" className="h-10 w-10">
          <Filter className="h-4 w-4" />
          <span className="sr-only">Filter</span>
        </Button>
      </div>

      {/* Patient tabs */}
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList>
          <TabsTrigger value="all">
            <Users className="h-4 w-4 mr-2" />
            All Patients
          </TabsTrigger>
          <TabsTrigger value="recent">
            <Calendar className="h-4 w-4 mr-2" />
            Recent Visits
          </TabsTrigger>
          <TabsTrigger value="upcoming">
            <Calendar className="h-4 w-4 mr-2" />
            Upcoming Appointments
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle>Patient Directory</CardTitle>
                <div className="text-sm text-muted-foreground">
                  {filteredPatients.length} {filteredPatients.length === 1 ? 'patient' : 'patients'}
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {isLoading ? (
                <div className="p-12 text-center">Loading patients...</div>
              ) : filteredPatients.length > 0 ? (
                <div className="divide-y">
                  {filteredPatients.map((patient) => (
                    <div key={patient.id} className="flex items-center justify-between p-4 hover:bg-muted/50">
                      <div className="flex items-center gap-4">
                        <Avatar className="h-10 w-10 border border-border">
                          <AvatarImage src={`https://i.pravatar.cc/150?u=${patient.id}`} />
                          <AvatarFallback>{`${patient.first_name[0]}${patient.last_name[0]}`}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{`${patient.first_name} ${patient.last_name}`}</p>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>{calculateAge(patient.date_of_birth)} years • {patient.gender || 'Unknown'}</span>
                            {patient.last_visit && (
                              <span>Last visit: {new Date(patient.last_visit).toLocaleDateString()}</span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Phone className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Mail className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <FileText className="h-4 w-4" />
                        </Button>
                        <Button variant="outline">View Chart</Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-12 text-center border border-dashed rounded-lg bg-muted/50 text-muted-foreground">
                  {searchQuery ? 'No patients match your search criteria.' : 'No patients found. Add your first patient to get started.'}
                </div>
              )}
            </CardContent>
            {filteredPatients.length > 0 && (
              <CardFooter className="border-t p-4 bg-muted/50">
                <div className="flex items-center justify-between w-full">
                  <div className="text-sm text-muted-foreground">
                    Showing {filteredPatients.length} of {patients.length} patients
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">Previous</Button>
                    <Button variant="outline" size="sm">Next</Button>
                  </div>
                </div>
              </CardFooter>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="recent" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Patient Visits</CardTitle>
              <CardDescription>Patients with visits in the last 30 days</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="p-12 text-center border border-dashed rounded-lg bg-muted/50 text-muted-foreground">
                This feature will be implemented soon.
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upcoming" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Appointments</CardTitle>
              <CardDescription>Patients with scheduled appointments</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="p-12 text-center border border-dashed rounded-lg bg-muted/50 text-muted-foreground">
                This feature will be implemented soon.
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}