import { signOut } from '@/lib/auth/auth-service';
import {
  authStateReducer,
  getOrganization,
  getUser
} from '@/lib/auth/auth-state-machine';
import { cacheOrganizationData } from '@/lib/auth/organization-cache';
import {
  clearOrganizationCache,
  fetchOrganizationData
} from '@/lib/auth/organization-service';
import { supabase } from '@/lib/supabase';
import type { AuthResponse, Session, UserResponse } from '@supabase/supabase-js';
import { useCallback, useEffect, useReducer, useRef, useState } from 'react';
import { AuthContext } from './auth-context';
import { AuthContextType, Organization } from './auth-context-types';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  // Use the state machine to manage auth state
  const [authState, dispatch] = useReducer(
    authStateReducer,
    { status: 'initializing' }
  );

  // Track session state
  const [session, setSession] = useState<Session | null>(null);

  // Track if component is mounted to avoid state updates after unmount
  const isMounted = useRef(true);

  // Track if we've already loaded organization data for the current user
  const loadedOrgForUser = useRef<string | null>(null);

  // Safety timeout to prevent infinite loading
  const [] = useState(() => Date.now());
  const [hasTimedOut, setHasTimedOut] = useState(false);

  // Auth methods
  const handleSignUp = useCallback(async (
    email: string,
    password: string,
    metadata?: { first_name?: string; last_name?: string; organization_id?: string }
  ): Promise<AuthResponse> => {
    return supabase.auth.signUp({
      email,
      password,
      options: { data: metadata }
    });
  }, []);

  const handleSignIn = useCallback(async (
    email: string,
    password: string
  ): Promise<AuthResponse> => {
    return supabase.auth.signInWithPassword({
      email: email,
      password: password
    });
  }, []);

  const handleResetPassword = useCallback(async (
    email: string
  ): Promise<{ data: object | null; error: Error | null }> => {
    try {
      const { data, error } = await supabase.auth.resetPasswordForEmail(email);
      return { data, error: error as Error | null };
    } catch (error) {
      return { data: null, error: error as Error };
    }
  }, []);

  const handleUpdatePassword = useCallback(async (
    newPassword: string
  ): Promise<UserResponse> => {
    return supabase.auth.updateUser({ password: newPassword });
  }, []);

  const handleSignOut = useCallback(async () => {
    const user = getUser(authState);
    if (user) {
      clearOrganizationCache(user.id);
    }
    await signOut();
  }, [authState]);

  const handleSetOrganization = useCallback((org: Organization | null) => {
    if (org) {
      console.info(`[AUTH] Setting organization: ${org.name} (${org.id})`);
      dispatch({ type: 'LOAD_ORGANIZATION', organization: org });

      // Also cache the organization with the improved caching mechanism
      const user = getUser(authState);
      if (user) {
        try {
          // Cache the organization with isLastSelected=true
          cacheOrganizationData(user.id, org, { isLastSelected: true });

          // Also store in localStorage directly for redundancy
          try {
            localStorage.setItem('spritely_last_org', org.id);
            localStorage.setItem('spritely_last_org_name', org.name);
            localStorage.setItem('spritely_last_org_user', user.id);
            console.info(`[AUTH] Cached organization selection: ${org.name} (${org.id}) for user ${user.id}`);
          } catch (_e) {
            console.warn('[AUTH] Failed to store organization in localStorage:', _e);
          }
        } catch (err) {
          window.console.warn('[AUTH] Failed to cache organization with improved caching:', err);
        }
      }
    } else {
      console.info('[AUTH] Clearing organization (sign out)');
      dispatch({ type: 'SIGN_OUT' });
    }
  }, [authState]);

  // Optimized organization data loading using the organization service
  const loadOrganizationData = useCallback(async () => {
    if (!session?.user) {
      console.debug('[AUTH] No user in session for loading organization');
      return;
    }

    const userId = session.user.id;

    // Check if we already have an organization loaded
    const currentOrg = getOrganization(authState);
    if (currentOrg) {
      console.debug(`[AUTH] Organization already loaded: ${currentOrg.name} (${currentOrg.id})`);
      loadedOrgForUser.current = userId;
      return;
    }

    // Check if we've already loaded organization data for this user
    if (loadedOrgForUser.current === userId) {
      console.debug('[AUTH] Already attempted to load organization for this user');
      return;
    }

    try {
      console.info(`[AUTH] Loading organization data for user: ${userId}`);
      loadedOrgForUser.current = userId;
      const organization = await fetchOrganizationData(
        session.user,
        session
      );

      console.info(`[AUTH] fetchOrganizationData returned:`, organization ? `${organization.name} (${organization.id})` : 'null');
      console.info(`[AUTH] isMounted.current:`, isMounted.current);

      if (isMounted.current) {
        if (organization) {
          console.info(`[AUTH] Successfully loaded organization: ${organization.name} (${organization.id})`);
          console.info(`[AUTH] Dispatching LOAD_ORGANIZATION action`);
          dispatch({
            type: 'LOAD_ORGANIZATION',
            organization: organization
          });
          console.info(`[AUTH] LOAD_ORGANIZATION action dispatched successfully`);
        } else {
          console.info('[AUTH] No organization found from fetchOrganizationData, checking system admin status');
          // No organization found for user - check if they're a system admin
          // and should get the default "All Organizations" view, but only if no cached org exists
          const checkSystemAdmin = async () => {
            try {
              // Check if user is a system admin first
              const { data: userRoles } = await supabase
                .from('user_roles')
                .select('role')
                .eq('user_id', session.user.id);

              const isSystemAdmin = userRoles?.some(role => role.role === 'system_admin');

              if (isSystemAdmin) {
                // For system admins, check if there's a cached organization selection
                const cachedOrgId = localStorage.getItem('spritely_last_org');
                const cachedUserId = localStorage.getItem('spritely_last_org_user');

                if (cachedOrgId && cachedUserId === session.user.id && cachedOrgId !== 'system-admin-no-org') {
                  // System admin has a cached specific organization, try to load it
                  // System admins can access any organization, even if they don't have a user_role for it
                  const { data: cachedOrg, error: orgError } = await supabase
                    .from('organizations')
                    .select('*')
                    .eq('id', cachedOrgId)
                    .single();

                  if (cachedOrg && !orgError) {
                    console.info(`System admin: Restored cached organization: ${cachedOrg.name} (${cachedOrg.id})`);
                    dispatch({
                      type: 'LOAD_ORGANIZATION',
                      organization: cachedOrg
                    });
                    return; // Exit early, we found the cached org
                  } else {
                    console.warn(`System admin: Cached organization ${cachedOrgId} not found or error:`, orgError);
                    // Clear invalid cache
                    localStorage.removeItem('spritely_last_org');
                    localStorage.removeItem('spritely_last_org_name');
                    localStorage.removeItem('spritely_last_org_user');
                  }
                }

                // No valid cached org, set the default "All Organizations" view for system admins
                console.info('System admin: Setting default "All Organizations" view');
                const defaultOrg = {
                  id: 'system-admin-no-org',
                  name: 'All Organizations',
                  type: 'system',
                  owner_id: 'system',
                  settings: {},
                  billing_info: {},
                  created_at: new Date().toISOString(),
                  subscription_tier: 'system',
                  updated_at: new Date().toISOString()
                };

                dispatch({
                  type: 'LOAD_ORGANIZATION',
                  organization: defaultOrg
                });
              }
            } catch (error) {
              console.error('Error checking system admin status:', error);
            }
          };

          checkSystemAdmin();
        }
      }
    } catch (error) {
      window.console.error('[AUTH] Error loading organization data:', error);
      // Reset the flag on error so we can try again
      loadedOrgForUser.current = null;
      if (isMounted.current) {
        dispatch({
          type: 'ERROR',
          error: new Error('Failed to load organization data')
        });
      }
    }
  }, [session?.user?.id]);

  // Safety timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (authState.status === 'authenticatedNoOrg') {
        console.warn('[AUTH] Loading timeout reached, forcing default organization');
        setHasTimedOut(true);

        // Force load a default organization to break out of loading state
        const defaultOrg = {
          id: 'timeout-fallback',
          name: 'Default Organization',
          type: 'fallback',
          owner_id: 'system',
          settings: {},
          billing_info: {},
          created_at: new Date().toISOString(),
          subscription_tier: 'free',
          updated_at: new Date().toISOString()
        };

        dispatch({
          type: 'LOAD_ORGANIZATION',
          organization: defaultOrg
        });
      }
    }, 15000); // 15 second timeout

    return () => clearTimeout(timeout);
  }, [authState.status]);

  // Combined auth state and session management
  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        const { data: { session: initialSession } } = await supabase.auth.getSession();

        if (mounted) {
          setSession(initialSession);

          if (initialSession?.user) {
            dispatch({ type: 'SIGN_IN', user: initialSession.user });
            loadOrganizationData();
          } else {
            dispatch({ type: 'SIGN_OUT' });
          }
        }
      } catch (error) {
        window.console.error('[AUTH] Error initializing auth:', error);
        if (mounted) {
          dispatch({ type: 'SIGN_OUT' });
        }
      }
    };

    initializeAuth();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, newSession) => {
        if (!mounted) return;

        setSession(newSession);

        if (newSession?.user) {
          const currentUser = getUser(authState);
          const isNewUser = !currentUser || currentUser.id !== newSession.user.id;

          if (isNewUser) {
            if (currentUser) {
              clearOrganizationCache(currentUser.id);
            }
            // Reset the loaded org flag for new user
            loadedOrgForUser.current = null;
            dispatch({ type: 'SIGN_IN', user: newSession.user });
            // Load organization data for new user
            loadOrganizationData();
          } else if (!getOrganization(authState)) {
            // Only load organization data if we don't already have it
            loadOrganizationData();
          }
        } else {
          // Reset the loaded org flag on sign out
          loadedOrgForUser.current = null;
          dispatch({ type: 'SIGN_OUT' });
        }
      }
    );

    return () => {
      mounted = false;
      isMounted.current = false;
      subscription.unsubscribe();
    };
  }, [loadOrganizationData]);

  // Build the context value
  const contextValue: AuthContextType = {
    user: getUser(authState),
    session,
    organization: getOrganization(authState),
    isLoading: (authState.status === 'initializing' || authState.status === 'authenticatedNoOrg') && !hasTimedOut,
    hasOrganization: !!getOrganization(authState),
    signOut: handleSignOut,
    signIn: handleSignIn,
    signUp: handleSignUp,
    resetPassword: handleResetPassword,
    updatePassword: handleUpdatePassword,
    setOrganization: handleSetOrganization
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}